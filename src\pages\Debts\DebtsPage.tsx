import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  Card,
  CardContent,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  Divider,
  LinearProgress,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Search,
  Payment,
  AccountBalance,
  Warning,
  CheckCircle,
  Cancel,
  Schedule,
  Receipt,
  CreditCard,
  Phone,
  Visibility,
  MonetizationOn,
  MoneyOff,
  Download,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';
import { CSVUtils, DEBT_COLUMNS } from '@/services/csv-utils';

// Components
import { CurrencyInput } from '@/components/CurrencyInput';

// Types
import { Debt, Payment as PaymentType, Sale } from '@/types';

// Utils
import { format, isAfter } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatSingleCurrency, formatDualCurrencyForCard } from '@/utils';

const DebtsPage: React.FC = () => {
  const [debts, setDebts] = useState<Debt[]>([]);
  const [filteredDebts, setFilteredDebts] = useState<Debt[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'overdue' | 'paid'>('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<'all' | 'paye' | 'impaye'>('all');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'banque' | 'mobile_money'>('cash');
  const [paymentNotes, setPaymentNotes] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 });
  const [sales, setSales] = useState<Sale[]>([]);
  const [showInlinePayment, setShowInlinePayment] = useState(false); // For inline payment form in debt details modal
  const [paymentCurrency, setPaymentCurrency] = useState<'CDF' | 'USD'>('CDF'); // Track selected currency for payments

  const permissions = adaptiveAuthService.getUserPermissions();

  // Helper function to get sale details for a debt
  const getSaleDetails = (debt: Debt) => {
    const sale = sales.find(s => s.id === debt.venteId);
    return sale;
  };

  // Helper function to get product names from a sale
  const getProductNames = (debt: Debt): string => {
    const sale = getSaleDetails(debt);
    if (!sale || !sale.produits || sale.produits.length === 0) {
      return 'Produits non disponibles';
    }

    return sale.produits.map(p => `${p.nomProduit} (x${p.quantite})`).join(', ');
  };

  // Helper function to get the actual client name (from debt or associated sale)
  const getClientName = (debt: Debt): string => {
    // If debt has a specific client name (not the default 'Client'), use it
    if (debt.nomClient && debt.nomClient.trim() !== '' && debt.nomClient !== 'Client') {
      return debt.nomClient;
    }

    // Otherwise, try to get the client name from the associated sale
    const sale = getSaleDetails(debt);
    if (sale && sale.nomClient && sale.nomClient.trim() !== '' && sale.nomClient !== 'Client') {
      return sale.nomClient;
    }

    // Fallback to 'Client' if no specific name is found
    return 'Client';
  };

  // Helper function to safely parse dates
  const parseDate = (dateString: string): Date | null => {
    try {
      const date = new Date(dateString);
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date found:', dateString);
        return null;
      }
      return date;
    } catch (error) {
      console.warn('Error parsing date:', dateString, error);
      return null;
    }
  };

  useEffect(() => {
    loadData();

    // Force initialization if no debts are found
    const checkAndInitializeDebts = async () => {
      const currentDebts = await adaptiveStorageService.getDebts();
      if (currentDebts.length === 0) {
        console.log('No debts found, forcing initialization...');
        await adaptiveStorageService.forceInitializeDebts();
        // Reload data after initialization
        setTimeout(() => loadData(), 500);
      }
    };

    checkAndInitializeDebts();
  }, []);

  useEffect(() => {
    filterDebts();
  }, [debts, searchTerm, statusFilter, paymentStatusFilter]);

  // Update rowsPerPage when filteredDebts changes and "Voir tout" is selected
  useEffect(() => {
    if (rowsPerPage === -1) {
      // Force re-render when showing all items and data changes
      setRowsPerPage(filteredDebts.length || 1);
    }
  }, [filteredDebts.length, rowsPerPage]);

  const loadData = async () => {
    try {
      const debtsData = await adaptiveStorageService.getDebts();
      const settingsData = await adaptiveStorageService.getSettings();
      const salesData = await adaptiveStorageService.getSales();

      // Validate and clean debt data
      const validDebts = debtsData.filter((debt: any) => {
        // Ensure required fields exist
        if (!debt.id || !debt.venteId) {
          console.warn('Invalid debt record missing required fields:', debt);
          return false;
        }

        // Ensure client name exists (set default if missing)
        if (!debt.nomClient || debt.nomClient.trim() === '') {
          debt.nomClient = 'Client';
        }

        // Ensure numeric fields are valid - preserve actual values, don't default to 0
        debt.montantTotalCDF = debt.montantTotalCDF !== undefined && debt.montantTotalCDF !== null && debt.montantTotalCDF !== ''
          ? Number(debt.montantTotalCDF)
          : 0;
        debt.montantPayeCDF = debt.montantPayeCDF !== undefined && debt.montantPayeCDF !== null && debt.montantPayeCDF !== ''
          ? Number(debt.montantPayeCDF)
          : 0;
        // Fix: Only set montantRestantCDF to calculated value if it's undefined/null, not if it's 0
        debt.montantRestantCDF = debt.montantRestantCDF !== undefined && debt.montantRestantCDF !== null && debt.montantRestantCDF !== ''
          ? Number(debt.montantRestantCDF)
          : debt.montantTotalCDF - debt.montantPayeCDF;

        return true;
      });

      // Update debt statuses based on due dates and validate calculations
      const updatedDebts = validDebts.map((debt: Debt) => {
        try {
          // First, validate and fix debt calculations
          const validatedDebt = validateDebtCalculations(debt);

          if (validatedDebt.statut === 'paid') return validatedDebt;

          const today = new Date();
          const dueDate = parseDate(validatedDebt.dateEcheance);

          if (validatedDebt.montantRestantCDF <= 0) {
            return { ...validatedDebt, statut: 'paid' as const, statutPaiement: 'paye' as const };
          } else if (dueDate && isAfter(today, dueDate)) {
            return { ...validatedDebt, statut: 'overdue' as const };
          } else {
            return { ...validatedDebt, statut: 'active' as const };
          }
        } catch (error) {
          console.warn('Error processing debt status:', debt, error);
          return debt; // Return original debt if processing fails
        }
      });

      setDebts(updatedDebts);
      setSettings(settingsData);
      setSales(salesData);
      await adaptiveStorageService.setDebts(updatedDebts);

      // Log debt data loading for debugging if needed
      if (process.env.NODE_ENV === 'development') {
        console.log('DebtsPage: Loaded', updatedDebts.length, 'debts');
      }
    } catch (error) {
      console.error('Error loading debt data:', error);
      setError('Erreur lors du chargement des données de dette.');
    }
  };

  const filterDebts = () => {
    try {
      let filtered = debts;

      // Search filter with null/undefined safety
      if (searchTerm && searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase().trim();
        filtered = filtered.filter(debt => {
          try {
            // Safely check each field with null/undefined protection
            const clientName = debt.nomClient?.toLowerCase() || '';
            const debtId = debt.id?.toLowerCase() || '';
            const saleId = debt.venteId?.toLowerCase() || '';
            const phone = debt.telephoneClient?.toLowerCase() || '';
            const products = getProductNames(debt).toLowerCase();

            return clientName.includes(searchLower) ||
                   debtId.includes(searchLower) ||
                   saleId.includes(searchLower) ||
                   phone.includes(searchLower) ||
                   products.includes(searchLower);
          } catch (error) {
            console.warn('Error filtering debt:', debt, error);
            return false; // Exclude problematic records from results
          }
        });
      }

      // Status filter with safety check
      if (statusFilter !== 'all') {
        filtered = filtered.filter(debt => debt.statut === statusFilter);
      }

      // Payment status filter with safety check
      if (paymentStatusFilter !== 'all') {
        filtered = filtered.filter(debt => debt.statutPaiement === paymentStatusFilter);
      }

      setFilteredDebts(filtered);
    } catch (error) {
      console.error('Error in filterDebts:', error);
      // Fallback to show all debts if filtering fails
      setFilteredDebts(debts);
      setError('Erreur lors de la recherche. Affichage de toutes les dettes.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'primary';
      case 'overdue': return 'error';
      case 'paid': return 'success';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Active';
      case 'overdue': return 'En retard';
      case 'paid': return 'Payée';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Schedule />;
      case 'overdue': return <Warning />;
      case 'paid': return <CheckCircle />;
      default: return <AccountBalance />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paye': return 'success';
      case 'impaye': return 'error';
      default: return 'default';
    }
  };

  const getPaymentStatusLabel = (status: string) => {
    switch (status) {
      case 'paye': return 'Payé';
      case 'impaye': return 'Impayé';
      default: return status;
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'paye': return <MonetizationOn />;
      case 'impaye': return <MoneyOff />;
      default: return <AccountBalance />;
    }
  };

  const handleOpenPaymentDialog = (debt: Debt) => {
    setSelectedDebt(debt);
    setPaymentAmount(debt.montantRestantCDF);
    setPaymentMethod('cash');
    setPaymentNotes('');
    setPaymentCurrency('CDF'); // Reset to default currency
    setOpenPaymentDialog(true);
    setError('');
    setSuccess('');
  };

  const handleClosePaymentDialog = () => {
    setOpenPaymentDialog(false);
    setSelectedDebt(null);
    setPaymentAmount(0);
    setPaymentMethod('cash');
    setPaymentNotes('');
    setPaymentCurrency('CDF'); // Reset to default currency
    setError('');
    setSuccess('');
  };

  const handleOpenViewDialog = (debt: Debt) => {
    setSelectedDebt(debt);
    setOpenViewDialog(true);
  };

  const handleCloseViewDialog = () => {
    setOpenViewDialog(false);
    setSelectedDebt(null);
    // Reset inline payment form state
    setShowInlinePayment(false);
    setPaymentAmount(0);
    setPaymentNotes('');
    setPaymentCurrency('CDF'); // Reset to default currency
    setError('');
    setSuccess('');
  };

  const handlePaymentStatusChange = async (debtId: string, newStatus: 'paye' | 'impaye') => {
    if (!permissions.canManageDebts) return;

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Êtes-vous sûr de vouloir changer le statut de paiement à "${getPaymentStatusLabel(newStatus)}" ?\n\n` +
      `Cette action modifiera le statut de paiement de la dette.`
    );

    if (!confirmed) return;

    const updatedDebts = debts.map(debt =>
      debt.id === debtId ? { ...debt, statutPaiement: newStatus } : debt
    );

    setDebts(updatedDebts);
    await adaptiveStorageService.setDebts(updatedDebts);

    // Update selectedDebt if it's the one being modified
    if (selectedDebt && selectedDebt.id === debtId) {
      setSelectedDebt({ ...selectedDebt, statutPaiement: newStatus });
    }

    setSuccess(`Statut de paiement mis à jour: ${getPaymentStatusLabel(newStatus)}`);

    setTimeout(() => {
      setSuccess('');
    }, 3000);
  };

  const handleAddPayment = async () => {
    if (!selectedDebt) return;

    // Validation
    if (paymentAmount <= 0) {
      setError('Le montant doit être supérieur à 0');
      return;
    }

    if (paymentAmount > selectedDebt.montantRestantCDF) {
      setError('Le montant ne peut pas dépasser le montant restant');
      return;
    }

    const now = new Date().toISOString();

    // Create payment with original currency preservation
    let montantCDF: number;
    let montantUSD: number;

    if (paymentCurrency === 'USD') {
      // User entered amount in USD, paymentAmount is converted to CDF by CurrencyInput
      montantUSD = paymentAmount / settings.tauxChangeUSDCDF; // Convert back to original USD amount
      montantCDF = paymentAmount; // CDF equivalent
    } else {
      // User entered amount in CDF
      montantCDF = paymentAmount;
      montantUSD = paymentAmount / settings.tauxChangeUSDCDF;
    }

    const newPayment: PaymentType = {
      id: `PAY-${Date.now()}`,
      montantCDF,
      montantUSD,
      methodePaiement: paymentMethod,
      datePaiement: now,
      notes: paymentNotes.trim() || undefined,
      deviseOriginale: paymentCurrency, // Store the original currency selection
    };

    // Update debt with payment
    const tempUpdatedDebt: Debt = {
      ...selectedDebt,
      montantPayeCDF: selectedDebt.montantPayeCDF + paymentAmount,
      montantPayeUSD: (selectedDebt.montantPayeUSD || 0) + (paymentAmount / settings.tauxChangeUSDCDF),
      paiements: [...selectedDebt.paiements, newPayment],
    };

    // Validate calculations and update status
    const validatedDebt = validateDebtCalculations(tempUpdatedDebt);
    const updatedDebt: Debt = {
      ...validatedDebt,
      statut: validatedDebt.montantRestantCDF <= 0 ? 'paid' : selectedDebt.statut,
      // Automatically update payment status based on amounts paid vs owed
      statutPaiement: getAutomaticPaymentStatus(validatedDebt),
    };

    // Update debts list
    const updatedDebts = debts.map(debt =>
      debt.id === selectedDebt.id ? updatedDebt : debt
    );

    setDebts(updatedDebts);
    await adaptiveStorageService.setDebts(updatedDebts);

    // Update selected debt for immediate modal refresh
    setSelectedDebt(updatedDebt);

    setSuccess('Paiement enregistré avec succès');

    // If using inline payment form, reset it after success
    if (showInlinePayment) {
      setTimeout(() => {
        setShowInlinePayment(false);
        setPaymentAmount(0);
        setPaymentNotes('');
        setPaymentCurrency('CDF'); // Reset to default currency
        setError('');
        setSuccess('');
      }, 2000);
    } else {
      // If using separate payment dialog, close it
      setTimeout(() => {
        handleClosePaymentDialog();
      }, 1500);
    }
  };



  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <Receipt />;
      case 'banque': return <CreditCard />;
      case 'mobile_money': return <Phone />;
      default: return <Receipt />;
    }
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'Cash';
      case 'banque': return 'Banque';
      case 'mobile_money': return 'Mobile Money';
      default: return method;
    }
  };

  // Validate and recalculate debt amounts to ensure consistency
  const validateDebtCalculations = (debt: Debt): Debt => {
    // Recalculate paid amount from payment history to ensure accuracy
    const totalPaidFromPayments = debt.paiements.reduce((sum, payment) => sum + payment.montantCDF, 0);
    const totalPaidUSDFromPayments = debt.paiements.reduce((sum, payment) => sum + (payment.montantUSD || 0), 0);

    // Use the calculated amounts from payments if they exist, otherwise use stored amounts
    const actualPaidCDF = debt.paiements.length > 0 ? totalPaidFromPayments : debt.montantPayeCDF;
    const actualPaidUSD = debt.paiements.length > 0 ? totalPaidUSDFromPayments : (debt.montantPayeUSD || 0);

    const calculatedRemainingCDF = debt.montantTotalCDF - actualPaidCDF;
    const calculatedRemainingUSD = debt.montantTotalUSD ? debt.montantTotalUSD - actualPaidUSD : undefined;

    // Ensure USD amounts are consistent with current exchange rate if missing
    const validatedTotalUSD = debt.montantTotalUSD || (debt.montantTotalCDF / settings.tauxChangeUSDCDF);
    const validatedPaidUSD = actualPaidUSD || (actualPaidCDF / settings.tauxChangeUSDCDF);
    const validatedRemainingUSD = calculatedRemainingUSD !== undefined
      ? Math.max(0, calculatedRemainingUSD)
      : Math.max(0, calculatedRemainingCDF / settings.tauxChangeUSDCDF);

    return {
      ...debt,
      // CRITICAL: Preserve the original montantTotalCDF - never override it
      montantTotalCDF: debt.montantTotalCDF, // Keep original total amount
      montantTotalUSD: validatedTotalUSD,
      montantPayeCDF: actualPaidCDF,
      montantPayeUSD: validatedPaidUSD,
      montantRestantCDF: Math.max(0, calculatedRemainingCDF), // Ensure remaining amount is never negative
      montantRestantUSD: validatedRemainingUSD
    };
  };

  const calculatePaymentProgress = (debt: Debt) => {
    if (debt.montantTotalCDF === 0) return 100;
    return Math.min(100, (debt.montantPayeCDF / debt.montantTotalCDF) * 100); // Ensure progress never exceeds 100%
  };

  // Automatic payment status calculation based on amounts
  const getAutomaticPaymentStatus = (debt: Debt): 'paye' | 'impaye' => {
    return debt.montantPayeCDF >= debt.montantTotalCDF ? 'paye' : 'impaye';
  };

  // Get payment status label with automatic calculation
  const getAutomaticPaymentStatusLabel = (debt: Debt) => {
    const automaticStatus = getAutomaticPaymentStatus(debt);
    return automaticStatus === 'paye' ? 'Payé' : 'Impayé';
  };

  // Get payment status color with automatic calculation
  const getAutomaticPaymentStatusColor = (debt: Debt): 'success' | 'error' => {
    const automaticStatus = getAutomaticPaymentStatus(debt);
    return automaticStatus === 'paye' ? 'success' : 'error';
  };

  // Get payment status icon with automatic calculation
  const getAutomaticPaymentStatusIcon = (debt: Debt) => {
    const automaticStatus = getAutomaticPaymentStatus(debt);
    return automaticStatus === 'paye' ? <CheckCircle fontSize="small" /> : <Cancel fontSize="small" />;
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    // Don't change page if showing all items
    if (rowsPerPage !== -1) {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setRowsPerPage(value);
    setPage(0); // Reset to first page when changing rows per page
  };

  // Export function for debts with Excel-compatible encoding
  const handleExportDebts = () => {
    // Use the standardized DEBT_COLUMNS for consistent export format
    const filename = `SmartBoutique_Dettes_${new Date().toISOString().split('T')[0]}.csv`;
    CSVUtils.downloadCSV(filteredDebts, DEBT_COLUMNS, filename);

    setSuccess('Dettes exportées en CSV avec succès (compatible Excel)');
    setTimeout(() => setSuccess(''), 3000);
  };

  // Stats
  const activeDebts = debts.filter(d => d.statut === 'active');
  const overdueDebts = debts.filter(d => d.statut === 'overdue');
  const payeDebts = debts.filter(d => d.statutPaiement === 'paye');
  const impayeDebts = debts.filter(d => d.statutPaiement === 'impaye');
  const totalDebtAmount = debts.reduce((sum, debt) => sum + debt.montantTotalCDF, 0);
  const totalRemainingAmount = debts.reduce((sum, debt) => sum + debt.montantRestantCDF, 0);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Gestion des Dettes
        </Typography>
        <Button
          variant="contained"
          startIcon={<Download />}
          onClick={handleExportDebts}
        >
          Exporter les Dettes
        </Button>
      </Box>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Dettes Actives
                  </Typography>
                  <Typography variant="h6">{activeDebts.length}</Typography>
                  <Typography variant="body2" color="primary" fontWeight="medium">
                    {formatDualCurrencyForCard(activeDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(activeDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(activeDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <Schedule color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    En Retard
                  </Typography>
                  <Typography variant="h6" color="error.main">{overdueDebts.length}</Typography>
                  <Typography variant="body2" color="error" fontWeight="medium">
                    {formatDualCurrencyForCard(overdueDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(overdueDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(overdueDebts.reduce((sum, d) => sum + d.montantRestantCDF, 0), settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <Warning color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Statut Payé
                  </Typography>
                  <Typography variant="h6" color="success.main">{payeDebts.length}</Typography>
                  <Typography variant="body2" color="error">
                    Impayé: {impayeDebts.length}
                  </Typography>
                </Box>
                <MonetizationOn color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Restant
                  </Typography>
                  <Typography variant="h6" fontWeight="medium">
                    {formatDualCurrencyForCard(totalRemainingAmount, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(totalRemainingAmount, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(totalRemainingAmount, settings.tauxChangeUSDCDF).secondaryAmount} sur {formatDualCurrencyForCard(totalDebtAmount, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(totalDebtAmount, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                </Box>
                <AccountBalance color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Rechercher par client, produit, ID dette ou ID vente..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Statut</InputLabel>
              <Select
                value={statusFilter}
                label="Statut"
                onChange={(e) => setStatusFilter(e.target.value as any)}
              >
                <MenuItem value="all">Tous</MenuItem>
                <MenuItem value="active">Actives</MenuItem>
                <MenuItem value="overdue">En retard</MenuItem>
                <MenuItem value="paid">Payées</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Statut Paiement</InputLabel>
              <Select
                value={paymentStatusFilter}
                label="Statut Paiement"
                onChange={(e) => setPaymentStatusFilter(e.target.value as any)}
              >
                <MenuItem value="all">Tous</MenuItem>
                <MenuItem value="paye">Payé</MenuItem>
                <MenuItem value="impaye">Impayé</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Debts Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Client</TableCell>
              <TableCell>Montant Dû</TableCell>
              <TableCell align="right">Payé</TableCell>
              <TableCell align="right">Restant</TableCell>
              <TableCell align="center">Progression</TableCell>
              <TableCell align="center">Statut</TableCell>
              <TableCell align="center">Statut Paiement</TableCell>
              <TableCell>Échéance</TableCell>
              {permissions.canManageDebts && <TableCell align="center">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {(rowsPerPage === -1 ? filteredDebts : filteredDebts.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage))
              .map((debt) => {
                try {
                  const progress = calculatePaymentProgress(debt);
                  const isOverdue = debt.statut === 'overdue';
                  return (
                    <TableRow
                      key={debt.id}
                      hover
                      onClick={() => handleOpenViewDialog(debt)}
                      sx={{ cursor: 'pointer' }}
                    >
                      <TableCell>
                        <Box>
                          <Typography variant="h6" fontWeight="bold" color="primary" gutterBottom>
                            {getClientName(debt)}
                          </Typography>
                          {debt.telephoneClient && (
                            <Typography variant="body2" color="text.secondary" display="block" sx={{ mb: 0.5 }}>
                              📞 {debt.telephoneClient}
                            </Typography>
                          )}
                          <Typography variant="body2" color="primary" fontWeight="medium" display="block" sx={{ mb: 0.5 }}>
                            🛍️ Produits: {getProductNames(debt)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block">
                            Créé le: {(() => {
                              try {
                                const creationDate = parseDate(debt.dateCreation);
                                return creationDate
                                  ? format(creationDate, 'dd/MM/yyyy', { locale: fr })
                                  : 'Date invalide';
                              } catch (error) {
                                console.warn('Error formatting creation date:', debt.dateCreation, error);
                                return 'Date invalide';
                              }
                            })()} • ID: {debt.venteId || 'N/A'}
                          </Typography>
                        </Box>
                    </TableCell>
                    <TableCell align="right">
                      {formatSingleCurrency(debt.montantTotalCDF, 'CDF')}
                    </TableCell>
                    <TableCell align="right">
                      {formatSingleCurrency(debt.montantPayeCDF, 'CDF')}
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        color={debt.montantRestantCDF > 0 ? 'error' : 'success.main'}
                      >
                        {formatSingleCurrency(debt.montantRestantCDF, 'CDF')}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ width: 100 }}>
                        <LinearProgress
                          variant="determinate"
                          value={progress}
                          color={progress === 100 ? 'success' : isOverdue ? 'error' : 'primary'}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {Math.round(progress)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        icon={getStatusIcon(debt.statut)}
                        label={getStatusLabel(debt.statut)}
                        color={getStatusColor(debt.statut)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        icon={getAutomaticPaymentStatusIcon(debt)}
                        label={getAutomaticPaymentStatusLabel(debt)}
                        color={getAutomaticPaymentStatusColor(debt)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        color={isOverdue ? 'error' : 'text.primary'}
                      >
                        {(() => {
                          const dueDate = parseDate(debt.dateEcheance);
                          return dueDate
                            ? format(dueDate, 'dd/MM/yyyy', { locale: fr })
                            : 'Date invalide';
                        })()}
                      </Typography>
                    </TableCell>
                    {permissions.canManageDebts && (
                      <TableCell align="center">
                        <Box display="flex" gap={1}>
                          <Tooltip title="Voir détails">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenViewDialog(debt)}
                            >
                              <Visibility fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {debt.statut !== 'paid' && (
                            <Tooltip title="Ajouter paiement">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleOpenPaymentDialog(debt)}
                              >
                                <Payment fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                );
                } catch (error) {
                  console.error('Error rendering debt row:', debt, error);
                  // Return a fallback row for problematic debt records
                  return (
                    <TableRow key={debt.id || `error-${Math.random()}`}>
                      <TableCell colSpan={permissions.canManageDebts ? 9 : 8}>
                        <Typography color="error" variant="body2">
                          Erreur d'affichage pour cette dette. ID: {debt.id || 'Inconnu'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  );
                }
              })}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[
            5,
            10,
            25,
            50,
            100,
            { label: 'Voir tout', value: -1 }
          ]}
          component="div"
          count={filteredDebts.length}
          rowsPerPage={rowsPerPage === -1 ? filteredDebts.length : rowsPerPage}
          page={rowsPerPage === -1 ? 0 : page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => {
            if (rowsPerPage === -1) {
              return `Affichage de tous les ${count} éléments`;
            }
            return `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`;
          }}
        />
      </TableContainer>

      {/* Payment Dialog */}
      <Dialog open={openPaymentDialog} onClose={handleClosePaymentDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Ajouter un Paiement</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {selectedDebt && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Client: {getClientName(selectedDebt)}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Montant restant: {formatSingleCurrency(selectedDebt.montantRestantCDF, 'CDF')}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <CurrencyInput
                  label="Montant du paiement"
                  value={paymentAmount}
                  onChange={(value) => setPaymentAmount(value)}
                  min={0}
                  max={selectedDebt.montantRestantCDF}
                  step={50}
                  exchangeRate={settings.tauxChangeUSDCDF}
                  required
                  showSlider={true}
                  allowUSDInput={true}
                  onCurrencyModeChange={(mode) => setPaymentCurrency(mode)}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Méthode de paiement</InputLabel>
                  <Select
                    value={paymentMethod}
                    label="Méthode de paiement"
                    onChange={(e) => setPaymentMethod(e.target.value as any)}
                  >
                    <MenuItem value="cash">Cash</MenuItem>
                    <MenuItem value="banque">Banque</MenuItem>
                    <MenuItem value="mobile_money">Mobile Money</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={2}
                  value={paymentNotes}
                  onChange={(e) => setPaymentNotes(e.target.value)}
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePaymentDialog}>Annuler</Button>
          <Button
            onClick={handleAddPayment}
            variant="contained"
            disabled={!selectedDebt || paymentAmount <= 0}
          >
            Enregistrer le Paiement
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Debt Dialog */}
      <Dialog open={openViewDialog} onClose={handleCloseViewDialog} maxWidth="md" fullWidth>
        <DialogTitle>Détails de la Dette</DialogTitle>
        <DialogContent>
          {selectedDebt && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                  Produits Achetés à Crédit:
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mt: 0.5, mb: 1 }}>
                  {getProductNames(selectedDebt)}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">ID Dette:</Typography>
                <Typography variant="body1">{selectedDebt.id}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Référence Vente:</Typography>
                <Typography variant="body2" color="text.secondary">{selectedDebt.venteId}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Client:</Typography>
                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>{getClientName(selectedDebt)}</Typography>
                {selectedDebt.telephoneClient && (
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">Téléphone:</Typography>
                    <Typography variant="body2" color="primary">
                      {selectedDebt.telephoneClient}
                    </Typography>
                  </Box>
                )}
                {selectedDebt.adresseClient && (
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">Adresse:</Typography>
                    <Typography variant="body2" color="text.primary">
                      {selectedDebt.adresseClient}
                    </Typography>
                  </Box>
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Statut:</Typography>
                <Chip
                  icon={getStatusIcon(selectedDebt.statut)}
                  label={getStatusLabel(selectedDebt.statut)}
                  color={getStatusColor(selectedDebt.statut)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Statut Paiement:</Typography>
                <Box display="flex" alignItems="center" gap={1} sx={{ mt: 0.5 }}>
                  {permissions.canManageDebts ? (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={selectedDebt.statutPaiement === 'paye'}
                          onChange={(e) => handlePaymentStatusChange(
                            selectedDebt.id,
                            e.target.checked ? 'paye' : 'impaye'
                          )}
                          color="success"
                          size="small"
                        />
                      }
                      label={
                        <Box display="flex" alignItems="center" gap={0.5}>
                          {getPaymentStatusIcon(selectedDebt.statutPaiement)}
                          <Typography variant="body2">
                            {getPaymentStatusLabel(selectedDebt.statutPaiement)}
                          </Typography>
                        </Box>
                      }
                      labelPlacement="end"
                    />
                  ) : (
                    <Chip
                      icon={getPaymentStatusIcon(selectedDebt.statutPaiement)}
                      label={getPaymentStatusLabel(selectedDebt.statutPaiement)}
                      color={getPaymentStatusColor(selectedDebt.statutPaiement)}
                    />
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2">Montant Dû:</Typography>
                <Typography variant="body1" color="primary" sx={{ fontWeight: 'bold' }}>
                  {formatSingleCurrency(selectedDebt.montantTotalCDF, 'CDF')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2">Montant Payé:</Typography>
                <Typography variant="body1" color="success.main">
                  {formatSingleCurrency(selectedDebt.montantPayeCDF, 'CDF')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2">Montant Restant:</Typography>
                <Typography variant="body1" color="error">
                  {formatSingleCurrency(selectedDebt.montantRestantCDF, 'CDF')}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>Progression du Paiement:</Typography>
                <Box sx={{ width: '100%', mb: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={calculatePaymentProgress(selectedDebt)}
                    color={
                      calculatePaymentProgress(selectedDebt) === 100
                        ? 'success'
                        : selectedDebt.statut === 'overdue'
                        ? 'error'
                        : 'primary'
                    }
                    sx={{ height: 10, borderRadius: 5 }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {Math.round(calculatePaymentProgress(selectedDebt))}% payé
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {selectedDebt.montantRestantCDF > 0
                        ? `${formatSingleCurrency(selectedDebt.montantRestantCDF, 'CDF')} restant`
                        : 'Entièrement payé'
                      }
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Date de Création:</Typography>
                <Typography variant="body1">
                  {(() => {
                    const creationDate = parseDate(selectedDebt.dateCreation);
                    return creationDate
                      ? format(creationDate, 'dd/MM/yyyy', { locale: fr })
                      : 'Date invalide';
                  })()}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Date d'Échéance:</Typography>
                <Typography
                  variant="body1"
                  color={selectedDebt.statut === 'overdue' ? 'error' : 'text.primary'}
                >
                  {(() => {
                    const dueDate = parseDate(selectedDebt.dateEcheance);
                    return dueDate
                      ? format(dueDate, 'dd/MM/yyyy', { locale: fr })
                      : 'Date invalide';
                  })()}
                </Typography>
              </Grid>

              {/* Payment History */}
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Historique des Paiements ({selectedDebt.paiements.length})
                </Typography>
                {selectedDebt.paiements.length === 0 ? (
                  <Alert severity="info">Aucun paiement enregistré</Alert>
                ) : (
                  <List dense>
                    {selectedDebt.paiements.map((payment, index) => (
                      <React.Fragment key={payment.id}>
                        <ListItem>
                          <ListItemText
                            primary={
                              <Box display="flex" alignItems="center" gap={1}>
                                {getPaymentMethodIcon(payment.methodePaiement)}
                                <Typography variant="body2">
                                  {payment.deviseOriginale === 'USD'
                                    ? `${formatSingleCurrency(payment.montantUSD || 0, 'USD')} (≈ ${formatSingleCurrency(payment.montantCDF, 'CDF')})`
                                    : `${formatSingleCurrency(payment.montantCDF, 'CDF')} (≈ ${formatSingleCurrency(payment.montantUSD || 0, 'USD')})`
                                  }
                                </Typography>
                                <Chip
                                  label={getPaymentMethodLabel(payment.methodePaiement)}
                                  size="small"
                                />
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="caption">
                                  {(() => {
                                    const paymentDate = parseDate(payment.datePaiement);
                                    return paymentDate
                                      ? format(paymentDate, 'dd/MM/yyyy HH:mm', { locale: fr })
                                      : 'Date invalide';
                                  })()}
                                </Typography>
                                {payment.notes && (
                                  <Typography variant="caption" display="block">
                                    {payment.notes}
                                  </Typography>
                                )}
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < selectedDebt.paiements.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                )}
              </Grid>

              {/* Inline Payment Form */}
              {permissions.canManageDebts && selectedDebt.statut !== 'paid' && showInlinePayment && (
                <Grid item xs={12}>
                  <Paper elevation={2} sx={{ p: 2, mt: 2, bgcolor: 'background.default' }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Payment />
                      Ajouter un Paiement
                    </Typography>

                    <Grid container spacing={2} sx={{ mt: 1 }}>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Montant restant: {formatSingleCurrency(selectedDebt.montantRestantCDF, 'CDF')}
                        </Typography>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <CurrencyInput
                          label="Montant à payer"
                          value={paymentAmount}
                          onChange={(value) => setPaymentAmount(value)}
                          min={0}
                          max={selectedDebt.montantRestantCDF}
                          step={50}
                          exchangeRate={settings.tauxChangeUSDCDF}
                          required
                          showSlider={true}
                          allowUSDInput={true}
                          onCurrencyModeChange={(mode) => setPaymentCurrency(mode)}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Méthode de paiement</InputLabel>
                          <Select
                            value={paymentMethod}
                            onChange={(e) => setPaymentMethod(e.target.value as 'cash' | 'banque' | 'mobile_money')}
                            label="Méthode de paiement"
                          >
                            <MenuItem value="cash">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Receipt fontSize="small" />
                                Cash
                              </Box>
                            </MenuItem>
                            <MenuItem value="mobile_money">
                              <Box display="flex" alignItems="center" gap={1}>
                                <Phone fontSize="small" />
                                Mobile Money
                              </Box>
                            </MenuItem>
                            <MenuItem value="banque">
                              <Box display="flex" alignItems="center" gap={1}>
                                <CreditCard fontSize="small" />
                                Banque
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Notes (optionnel)"
                          value={paymentNotes}
                          onChange={(e) => setPaymentNotes(e.target.value)}
                          multiline
                          rows={2}
                          placeholder="Ajouter des notes sur ce paiement..."
                        />
                      </Grid>

                      {error && (
                        <Grid item xs={12}>
                          <Alert severity="error">{error}</Alert>
                        </Grid>
                      )}

                      {success && (
                        <Grid item xs={12}>
                          <Alert severity="success">{success}</Alert>
                        </Grid>
                      )}

                      <Grid item xs={12}>
                        <Box display="flex" gap={1} justifyContent="flex-end">
                          <Button
                            variant="outlined"
                            onClick={() => {
                              setShowInlinePayment(false);
                              setPaymentAmount(0);
                              setPaymentNotes('');
                              setPaymentCurrency('CDF'); // Reset to default currency
                              setError('');
                              setSuccess('');
                            }}
                          >
                            Annuler
                          </Button>
                          <Button
                            variant="contained"
                            color="success"
                            onClick={() => {
                              // Set to full amount for quick full payment
                              setPaymentAmount(selectedDebt.montantRestantCDF);
                              setPaymentNotes('Paiement complet');
                            }}
                            startIcon={<Payment />}
                          >
                            Paiement Complet
                          </Button>
                          <Button
                            variant="contained"
                            color="primary"
                            onClick={handleAddPayment}
                            startIcon={<Payment />}
                            disabled={paymentAmount <= 0}
                          >
                            Payer
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {selectedDebt.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Notes:</Typography>
                  <Typography variant="body1">{selectedDebt.notes}</Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          {/* Payment functionality integrated into debt details modal */}
          {permissions.canManageDebts && selectedDebt && selectedDebt.statut !== 'paid' && !showInlinePayment && (
            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                // Show inline payment form
                setShowInlinePayment(true);
                setPaymentAmount(0);
                setPaymentMethod('cash');
                setPaymentNotes('');
                setError('');
                setSuccess('');
              }}
              startIcon={<Payment />}
              sx={{ mr: 1 }}
            >
              Ajouter Paiement
            </Button>
          )}
          <Button onClick={handleCloseViewDialog}>Fermer</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DebtsPage;
